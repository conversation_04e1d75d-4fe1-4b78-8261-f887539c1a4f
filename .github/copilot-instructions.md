# Real Estate Financial Modeling System - AI Development Guide

## Architecture Overview

This is an **AI-powered real estate financial modeling system** built with Claude Code sub-agents and direct Excel manipulation (no browser automation). The system follows a **multi-agent orchestration pattern** where specialized AI agents handle different aspects of financial modeling.

### Core Agent Architecture
- **Workflow Orchestrator** (`src/core/workflow_orchestrator.py`) - Central coordination hub
- **6 Specialized Sub-Agents** in `.claude/agents/` - Data analysis, financial modeling, validation, Excel enhancement, risk analysis, reporting
- **Agent Communication** - Sequential pipeline with structured data handoffs via JSON/Excel files
- **Configuration-Driven** - YAML frontmatter in `.claude/agents/*.md` files defines agent behavior and tool access

### Key Integration Patterns
- **Direct Excel Processing** - Uses `InstitutionalExcelProcessor` instead of browser automation for security/performance
- **tryshortcut.ai Replacement** - Local Excel manipulation with openpyxl for institutional-grade formatting
- **Async Agent Execution** - All agent methods are async with performance tracking (<30s requirements)
- **Validation Framework** - Comprehensive financial accuracy, regulatory compliance, and data quality checks

## Development Workflows

### Testing Commands
```bash
# Environment validation (run first)
python tests/test_environment.py

# Core integration tests
python -m pytest tests/test_phase5_simple.py -v  # 8 core tests
python -m pytest tests/test_validation_framework.py -v  # 25 validation tests  
python -m pytest tests/test_excel_risk_integration.py -v  # 10 integration tests

# Performance validation (all operations must complete <30s)
python -m pytest tests/test_risk_analysis_integration.py::TestRiskAnalysisIntegration::test_performance_requirement -xvs
```

### Agent Development Pattern
1. Create/modify `.claude/agents/{agent-name}.md` with YAML frontmatter
2. Update `WorkflowOrchestrator._execute_agent()` method for new agent types
3. Add corresponding test in `tests/test_enhanced_workflow_orchestrator.py`
4. Ensure agent outputs follow structured format: `{'cleaned_data': ..., 'market_analysis': ...}`

### Configuration Files
- `config/workflow_config.yaml` - Agent timeouts, retry settings, property templates
- `config/validation_config.yaml` - Financial accuracy rules, compliance thresholds
- `.claude/settings.local.json` - Pre-approved pytest commands for faster development

## Project-Specific Conventions

### Error Handling & Retry Logic
```python
# All agents use consistent retry pattern
agent_config = self.agent_configs[agent_name]
timeout = agent_config.timeout or 300
retry_count = agent_config.retry_count or 2
```

### Excel Enhancement Pattern
```python
# Use InstitutionalExcelProcessor directly, not browser automation
response = await excel_processor.enhance_excel_model(
    file_path=excel_file,
    enhancements=["dcf", "monte_carlo", "sensitivity_analysis"]
)
```

### Validation Standards
- **Financial Accuracy**: IRR/NPV calculations, DSCR ≥1.20, LTV ≤0.80
- **East Baton Rouge Compliance**: UDC schema integration for zoning validation
- **Performance Requirements**: All operations <30s, Monte Carlo 10K simulations <30s

### Test Fixtures Pattern
```python
@pytest.fixture
def sample_excel_file(self):
    """Standard pattern for Excel test files"""
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
        # Create workbook with required sheets: Assumptions, Cash Flow, etc.
        yield tmp.name
    os.unlink(tmp.name)
```

## Critical Integration Points

### Agent Data Flow
1. **Data Analysis** → cleaned_data, market_analysis
2. **Financial Modeling** → financial_model, metrics  
3. **Excel Enhancement** → enhanced_file_path, applied_enhancements
4. **Validation** → validation_passed, compliance_status
5. **Risk Analysis** → monte_carlo_results, sensitivity_results
6. **Reporting** → final_reports, stakeholder_outputs

### Property Type Templates
- Located in `src/models/property_templates.py`
- Support: multifamily, office, retail, industrial, development
- Each type has specific cash flow modules and validation rules

### External Dependencies (Minimal)
- **No browser automation** - Replaced with direct Excel processing
- **Local processing only** - No external API calls during modeling
- **Core libs**: pandas, openpyxl, numpy, scipy for calculations

## Common Pitfalls & Solutions

1. **Agent Timeout Issues** - Increase timeout in `workflow_config.yaml`, not hardcoded values
2. **Excel File Corruption** - Always use `tempfile.NamedTemporaryFile` with proper cleanup
3. **Test Async Issues** - Use `@pytest.mark.asyncio` and `pytest-asyncio` for all agent tests
4. **Performance Degradation** - Monitor via `execution_time` tracking in `AgentResult`

## Quick Start for New Features

1. **New Agent**: Create `.claude/agents/new-agent.md` → Update `AgentType` enum → Add execution method
2. **New Property Type**: Extend `property_templates.py` → Update `workflow_config.yaml` templates
3. **New Validation Rule**: Add to `validation_framework.py` → Update `validation_config.yaml`
4. **Performance Optimization**: Profile with `time.time()` tracking, target <30s for all operations

The system prioritizes **institutional-grade quality** with **local processing security** over external service dependencies.
